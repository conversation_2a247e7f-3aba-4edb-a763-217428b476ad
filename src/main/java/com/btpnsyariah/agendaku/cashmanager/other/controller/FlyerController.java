package com.btpnsyariah.agendaku.cashmanager.other.controller;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerDTO;
import com.btpnsyariah.agendaku.cashmanager.other.service.FlyerService;
import com.btpnsyariah.agendaku.cashmanager.util.Constants;
import com.btpnsyariah.agendaku.cashmanager.util.DataFoundException;
import com.btpnsyariah.agendaku.cashmanager.util.MinioService;
import com.btpnsyariah.agendaku.cashmanager.util.Utility;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/flyer")
public class FlyerController {

    @Autowired
    FlyerService flyerService;
    @Autowired
    MinioService minioService;
    @Autowired
    ObjectMapper objectMapper;

    @Operation(summary = "Submit Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity persistFlyer(@RequestHeader("Access-Token") String token,
                                       @RequestParam("file") MultipartFile file,
                                       @Schema(example = Constants.REQUEST_BODY_Flyer) @RequestParam("data") String jsonData){
        try {
            FlyerDTO flyerData = objectMapper.readValue(jsonData, FlyerDTO.class);
            flyerService.persistFlyer(file, flyerData, token);
            return new ResponseEntity<>("Flyer persisted!", HttpStatus.OK);
        } catch (DataFoundException e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.CONFLICT);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @Operation(summary = "Get All Flyers")
    @GetMapping()
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity getFlyers(@RequestParam(required = false, value = "version") String version,
                                              @RequestParam(required = false, value = "startDate") String startDate,
                                              @RequestParam(required = false, value = "endDate") String endDate,
                                              Pageable pageable){
        try {
            return new ResponseEntity<>(flyerService.getAllFlyers(version,startDate,endDate, pageable), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Download Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @GetMapping("/download")
    public ResponseEntity downloadFlyer(@RequestParam(required = false, value = "id") Long id,
                                       @RequestHeader("Access-Token") String token){
        try {
            String fileName = flyerService.getFlyerFileName(id);
            String fileExtension = Utility.getFileNameExtension(fileName);
            MediaType mediaType = Utility.getMediaTypeFromExtension("." + fileExtension);

            byte[] inputBytes = flyerService.downloadFlyer(id, token);

            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .body(inputBytes);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Delete Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @DeleteMapping("/delete")
    public ResponseEntity deleteFlyer(@RequestParam("id") Long id){
        try {
            flyerService.deleteFlyer(id);
            return new ResponseEntity<>(String.format("Flyer with id %s deleted successfully!", id), HttpStatus.OK);
        } catch (DataFoundException e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Update Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @PutMapping(value = "/update")
    public ResponseEntity updateFlyer(@RequestHeader("Access-Token") String token,
                                       @RequestParam(required = false, value = "file" ) MultipartFile file,
                                       @Schema(example = Constants.REQUEST_BODY_Flyer) @RequestParam("data") String jsonData){
        try {
            FlyerDTO flyerData = objectMapper.readValue(jsonData, FlyerDTO.class);
            flyerService.updateFlyer(file, flyerData, token);
            return new ResponseEntity<>(String.format("Flyer with id %s updated successfully!", flyerData.getId()), HttpStatus.OK);
        } catch (DataFoundException e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.CONFLICT);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
