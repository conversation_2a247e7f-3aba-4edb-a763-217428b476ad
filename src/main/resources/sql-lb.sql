SELECT btd.id id, btd.InsertedDate TransactionDate, btd.TransactionId TransactionId, btd.InsertedBy RequestedType, btd.MMSCode MMSCode,
                CASE 
            when btd.InsertedBy = 'COVER_DANA' THEN 
            CASE
            WHEN cd.RequestType = 'COVER IN' THEN 'Masuk - Cover In'
            ELSE 'Keluar - Cover Out'
            END
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN 'Selisih Cash Opname'
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN 'Selisih Cash Count'
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN 'Tukar Denom (Masuk) CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = mein.CreatedBy),mein.CreatedBy)
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN '<PERSON><PERSON>om (Keluar) CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = meout.CreatedBy),meout.CreatedBy)
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN 'Cash Opname'
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' AND ccnt.GeneratedBy IS NOT NULL THEN 'Auto Cash Count'
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN 'Cash Count'
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN
            CASE
            WHEN kw.TransactionType = 'DISTRIBUTION' THEN ISNULL(kw.DailyTransactionType,'')+' Keluar - CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = kw.CreatedBy),kw.CreatedBy)
            ELSE ISNULL(kw.DailyTransactionType,'')+' Masuk - CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = kw.CreatedBy),kw.CreatedBy)
            END
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN
            CASE WHEN lb.RequestedType = 'DANA_OPERATIONAL' THEN
            CASE WHEN dolb.id IS NOT NULL THEN
            CASE WHEN dolb.[Type] = 'BRW' THEN
            CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'Masuk - Dana Operasional BRW'
            ELSE 'Keluar - Dana Operasional BRW'
            END
            ELSE
            CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'Masuk - Dana Operasional Lainnya'
            ELSE 'Keluar - Dana Operasional Lainnya'
            END
            END
            ELSE
            CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'Masuk - Dana Operasional'
            ELSE 'Keluar - Dana Operasional'
            END
            END
            WHEN lb.RequestedType = 'FPB' THEN CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'FPB Masuk - ' + ISNULL((
            SELECT
            TOP 1 CoName
            FROM
            CashUserPersonas
            WHERE
            NIK = fpbr.CreatedBy OR NIK = fpbrev.CreatedBy
            ORDER BY
            UpdatedDate DESC),
            '')
            ELSE 'FPB Keluar - ' + ISNULL((
            SELECT
            TOP 1 CoName
            FROM
            CashUserPersonas
            WHERE
            NIK = fpbr.CreatedBy OR NIK = fpbrev.CreatedBy
            ORDER BY
            UpdatedDate DESC),
            '')
            END
            WHEN lb.RequestedType = 'FPB_REVERSAL' THEN CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'Masuk Koreksi FPB - ' + ISNULL((
            SELECT
            TOP 1 CoName
            FROM
            CashUserPersonas
            WHERE
            NIK = fpbrev.CreatedBy
            ORDER BY
            UpdatedDate DESC),
            fpbr.CreatedBy)
            ELSE 'Keluar Koreksi FPB - ' + ISNULL((
            SELECT
            TOP 1 CoName
            FROM
            CashUserPersonas
            WHERE
            NIK = fpbrev.CreatedBy
            ORDER BY
            UpdatedDate DESC),
            fpbr.CreatedBy)
            END
            ELSE
            CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN ISNULL(lb.DailyTransactionType,'')+' Masuk - Kas Wisma'
            ELSE ISNULL(lb.DailyTransactionType,'')+' Keluar - Kas Wisma'
            END
            END
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN 'Koreksi Transaksi LB' + CAST(rt.TransactionId as varchar(max))
                when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN 'Koreksi Transaksi Cover Dana CD' + CAST(rt.TransactionId as varchar(max))
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN 'Tukar Denom (Masuk) CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = mein.CreatedBy),mein.CreatedBy)
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN 'Tukar Denom (Keluar) CO ' + ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = meout.CreatedBy),meout.CreatedBy)
            when btd.InsertedBy = 'RESET_MMS' THEN 'Reset MMS - ' +  ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = mmsrl.InsertedBy), mmsrl.InsertedBy)
                END Explanation,
                    case when btd.InsertedBy = 'COVER_DANA' THEN ISNULL(cd.CollectedAmount,0)
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.TotalAmount
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN btd.EndingBalanceAmount
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN cctd.TotalAmount
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN btd.EndingBalanceAmount
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.TotalAmount
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.TotalAmount
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN rt.Amount
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN rt.Amount
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.TotalAmount
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.TotalAmount
            when btd.InsertedBy = 'RESET_MMS' THEN mmsrl.BalanceAmount
            END TotalAmount, 
            case when btd.InsertedBy = 'COVER_DANA' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = cd.RequestedByNik),cd.RequestedByNik)
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.CreatedBy
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = cont.ExecutedBy),cont.CreatedBy)
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN cctd.CreatedBy
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = ccnt.ExecutedBy),ccnt.CreatedBy)
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = kw.CreatedBy),kw.CreatedBy)
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN  ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = lb.CreatedBy),lb.CreatedBy)
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = rt.CreatedBy),rt.CreatedBy)
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = rt.CreatedBy),rt.CreatedBy)
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = mein.CreatedBy),mein.CreatedBy)
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = meout.CreatedBy),meout.CreatedBy)
            when btd.InsertedBy = 'RESET_MMS' THEN ISNULL((select top 1 cu.COName from CashUserPersonas cu where cu.NIK = mmsrl.InsertedBy), mmsrl.InsertedBy)
            END ExecutedBy,
                case when btd.InsertedBy = 'COVER_DANA' THEN cd.Hundreds
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.HundredThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.hundredThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.HundredThousandsBundle * 100) + cctd.HundredThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.HundredThousandsBundle * 100 ) + ccnt.hundredThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Hundreds
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Hundreds
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.Hundreds
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.Hundreds
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.HundredThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.HundredThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END Hundreds, 
                IsNULL(CASE when btd.InsertedBy = 'COVER_DANA' THEN cd.SeventyFives
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.SeventyFiveThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.SeventyFiveThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.SeventyFiveThousandsBundle * 100) + cctd.SeventyFiveThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.SeventyFiveThousandsBundle * 100) + ccnt.SeventyFiveThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.SeventyFives
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.SeventyFives
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.SeventyFives
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.SeventyFives
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.SeventyFiveThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.SeventyFiveThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END ,0) SeventyFives, 
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.Fiftys
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.FiftyThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.fiftyThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.FiftyThousandsBundle * 100) + cctd.FiftyThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.FiftyThousandsBundle * 100) + ccnt.FiftyThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Fiftys
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Fiftys
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.Fiftys
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.Fiftys
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.FiftyThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.FiftyThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END Fiftys, 
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.Twentys
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.TwentyThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.twentyThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.TwentyThousandsBundle * 100) + cctd.TwentyThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.TwentyThousandsBundle * 100) + ccnt.twentyThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Twentys
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Twentys
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.Twentys
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.Twentys
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.TwentyThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.TwentyThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END Twentys, 
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.Tens
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.TenThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.tenThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.TenThousandsBundle * 100) + cctd.TenThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.TenThousandsBundle * 100) + ccnt.tenThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Tens
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Tens
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.Tens
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.Tens
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.TenThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.TenThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END Tens, 
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.Fives
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.FiveThousands
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.fiveThousands
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN (cctd.FiveThousandsBundle * 100) + cctd.FiveThousands
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN (ccnt.FiveThousandsBundle * 100) + ccnt.fiveThousands
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Fives
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Fives
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.Fives
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.Fives
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN mein.FiveThousands
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN meout.FiveThousands
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END Fives, 
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.SmallMoney
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.SmallMoney
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN cont.smallMoney
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN cctd.SmallMoney
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN ccnt.smallMoney
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.SmallMoney
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.SmallMoney
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN lbrt.SmallMoney
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN cdrt.SmallMoney
            when btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' THEN ISNULL(mein.SmallMoney,mein.TwoThousands*2000+mein.OneThousands*1000+mein.FiveHundreds*500+mein.TwoHundreds*200+mein.OneHundreds*100)
            when btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' THEN ISNULL(meout.SmallMoney,meout.TwoThousands*2000+meout.OneThousands*1000+meout.FiveHundreds*500+meout.TwoHundreds*200+meout.OneHundreds*100)
            when btd.InsertedBy = 'RESET_MMS' THEN 0
            END [SmallMoney],
            case when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN btd.BeginningBalanceAmount
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN btd.BeginningBalanceAmount
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN btd.BeginningBalanceAmount
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN btd.BeginningBalanceAmount
            else btd.EndingBalanceAmount
            END BalanceAmount,
            case when btd.InsertedBy = 'COVER_DANA' THEN cd.Comments
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN cotd.Notes
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN cctd.Notes
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN kw.Notes
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN lb.Notes
            when btd.InsertedBy IN ('REVERSE_TRANSACTION','REVERSE_TRANSACTION_CD') THEN rt.Notes
            when btd.InsertedBy = 'RESET_MMS' THEN ''
            else ''
            END Notes,
                CASE 
            when btd.InsertedBy = 'COVER_DANA' THEN 
            CASE
            WHEN cd.RequestType = 'COVER IN' THEN 'IN'
            ELSE 'OUT'
            END
            when btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' THEN 'OUT'
            when btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' THEN 
            CASE
            WHEN cont.cashOpnameType = 'CASH_OPNAME_IN' THEN 'IN'
            ELSE 'OUT'
            END
            when btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' THEN 'OUT'
            when btd.InsertedBy = 'CASH_COUNT_TRANSACTION' THEN 
            CASE
            WHEN ccnt.CashCountType = 'CASH_COUNT_IN' THEN 'IN'
            ELSE 'OUT'
            END
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN
            CASE
            WHEN kw.TransactionType = 'DISTRIBUTION' THEN 'OUT'
            ELSE 'IN'
            END
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN
            CASE
            WHEN lb.TransactionType = 'CASH_IN' THEN 'IN'
            WHEN lb.TransactionType = 'CASH_OUT' THEN 'OUT'
            END
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'LB_DAILY_TRANSACTION' THEN
            CASE
            WHEN lbrt.TransactionType = 'CASH_IN' THEN 'OUT'
            WHEN lbrt.TransactionType = 'CASH_OUT' THEN 'IN'
            END
            when btd.InsertedBy = 'REVERSE_TRANSACTION' AND rt.RequestedType = 'KW_DAILY_TRANSACTION' THEN
            CASE
            WHEN kw.TransactionType = 'DISTRIBUTION' THEN 'IN'
            ELSE 'OUT'
            END
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN 
            CASE
            WHEN cd.RequestType = 'COVER OUT' THEN 'IN'
            ELSE 'OUT'
            END
            when btd.InsertedBy = 'RESET_MMS' THEN 'OUT'
                END ReportTransactionType,
                GETDATE() CreatedDate,
            '' CreatedBy,
            CASE
            when btd.InsertedBy = 'KW_DAILY_TRANSACTION' THEN ISNULL(kw.Condition,'NORMAL')
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN ISNULL(lb.Condition,'NORMAL')
            when btd.InsertedBy = 'COVER_DANA' THEN ISNULL (cd.Condition,'NORMAL')
            when btd.InsertedBy = 'REVERSE_TRANSACTION' THEN ISNULL(rt.Condition,'NORMAL')
            when btd.InsertedBy = 'REVERSE_TRANSACTION_CD' THEN ISNULL(rt.Condition,'NORMAL')
            ELSE 'NORMAL'
            END Condition,
            btd.InsertedDate RequestedDate,

            CASE
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN isnull(lb.LBOpenHour,'-') 
            when btd.InsertedBy = 'COVER_DANA' THEN isnull(cd.LBOpenHour,'-') 
            ELSE '-'
            END openHour,

            CASE
            when btd.InsertedBy = 'LB_DAILY_TRANSACTION' THEN isnull(lb.LBCloseHour,'-') 
            when btd.InsertedBy = 'COVER_DANA' THEN isnull(cd.LBCloseHour,'-') 
            ELSE '-'
            END closeHour

            FROM BalanceTransactionDetail btd
            left join CoverDanaRequest cd on btd.InsertedBy = 'COVER_DANA' and cd.id = btd.TransactionId
            left join CashOpnameTransactionDifference cotd on btd.InsertedBy = 'CASH_OPNAME_DIFF_TRANSACTION' and cotd.id = btd.TransactionId
            left join CashOpnameTransaction cont on btd.InsertedBy = 'CASH_OPNAME_TRANSACTION' and cont.id = btd.TransactionId
            left join CashCountTransactionDifference cctd on btd.InsertedBy = 'CASH_COUNT_DIFF_TRANSACTION' and cctd.id = btd.TransactionId
            left join CashCountTransaction ccnt on btd.InsertedBy = 'CASH_COUNT_TRANSACTION' and ccnt.id = btd.TransactionId
            left join KWDailyTransaction kw on btd.InsertedBy = 'KW_DAILY_TRANSACTION' and kw.id = btd.TransactionId
                left join LBDailyTransaction lb on btd.InsertedBy = 'LB_DAILY_TRANSACTION' and lb.id = btd.TransactionId
            left join DanaOperational dolb on btd.InsertedBy = 'LB_DAILY_TRANSACTION' and lb.RequestedType = 'DANA_OPERATIONAL' and dolb.LBDailyId = lb.id
            left join ReverseTransactionLog rt on btd.InsertedBy IN ('REVERSE_TRANSACTION','REVERSE_TRANSACTION_CD') and rt.id = btd.TransactionId
            left join LBDailyTransaction lbrt on btd.InsertedBy = 'REVERSE_TRANSACTION' and rt.RequestedType = 'LB_DAILY_TRANSACTION' and lbrt.id = rt.TransactionId
            left join MoneyExchangeTransaction mein on btd.InsertedBy = 'EXCHANGE_IN_TRANSACTION' and mein.id = btd.TransactionId AND mein.MoneyExchangeType = 'CASH_IN'
            left join MoneyExchangeTransaction meout on btd.InsertedBy = 'EXCHANGE_OUT_TRANSACTION' and meout.id = btd.TransactionId AND meout.MoneyExchangeType = 'CASH_OUT'
            left join CoverDanaRequest cdrt on btd.InsertedBy = 'REVERSE_TRANSACTION_CD' and rt.RequestedType = 'COVER_DANA' and cdrt.id = rt.TransactionId
            left join MMSResetLog mmsrl on btd.InsertedBy = 'RESET_MMS' and btd.TransactionId = mmsrl.id
            left join FPBRequest fpbr on (fpbr.LbDailyTransactionId = lb.id OR fpbr.AdditionalSettlementId = lb.id OR fpbr.RefundId = lb.id)
            left join FPBReversal fpbrev on (fpbrev.ReverseSettlementId = lb.id OR fpbrev.AdditionalSettlementId = lb.id)
            where btd.BalanceTransactionType = 'LB_VAULT' 
            AND CONVERT(date, dateadd(HOUR,7,btd.InsertedDate)) >= CONVERT(date, dateadd(HOUR,7,:dateFrom))
            AND CONVERT(date, dateadd(HOUR,7,btd.InsertedDate)) <= CONVERT(date, dateadd(HOUR,7,:dateTo))
            AND btd.MMSCode = :mmsCode
            order by btd.id asc 