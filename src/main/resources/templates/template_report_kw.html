<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Kas Wisma Report</title>
    <style>

        h1{
            font-size: 21px;
            font-weight: bold;
            color: #000000;
        }
        table.redTable tbody td.left{
            text-align: left;
        }
        table.redTable tbody td.center{
            text-align: center;
        }
        table.redTable {
            border: 2px solid black;
            background-color: #EEE7DB;
            width: 120%;
            border-collapse: collapse;
            overflow-x: scroll;
        }
        table.redTable tbody td {
            border: 1px solid black;
            padding: 3px;
            text-align: right;
        }
        table.redTable tbody td.title {
            background: #F37022;
            font-size: 19px;
            font-weight: bold;
            color: white;
        }
       // table.redTable tbody tr:nth-child(even) {
       //    background: #F3C8BF;
       // }
        table.redTable tbody tr.isf{
            background: #baebb5;
        }
        table.redTable thead {
            background: #F37022;
        }
        table.redTable thead th {
            font-size: 19px;
            font-weight: bold;
            color: #FFFFFF;
            text-align: center;
            border: 2px solid rgb(0, 0, 0);
        }
        table.redTable thead th.child {
            font-size: 15px;
            font-weight: normal;
            color: #FFFFFF;
            text-align: center;
            border: 2px solid rgb(0, 0, 0);
        }
        table.redTable tbody tr.con{
            background: #b0e2e8;
        }
    </style>
</head>
<body>
    <h1>CATATAN KAS WISMA</h1>
    <h2></h2>
    <h2></h2>

    <table>
        <tr>
            <td>BM AREA</td>
            <td>: </td>
            <td th:text="${bmArea}"></td>
        </tr>
        <tr>
            <td>NAMA MMS / WISMA</td>
            <td>: </td>
            <td th:text="${mmsName}"></td>
        </tr>
        <tr>
            <td>KODE MMS / WISMA</td>
            <td>: </td>
            <td th:text="${mmsCode}"></td>
        </tr>
    </table>

    <h2></h2>
    <h2></h2>

    <table class="redTable">
        <thead>
                <tr class="headings">
                    <th rowspan="2">Referensi</th>
                    <th rowspan="2">Tanggal Transaksi</th>
                    <th rowspan="2">Tanggal Request</th>
                    <th rowspan="2">Deskripsi</th>
                    <th colspan="6">Uang Kertas</th>
                    <th rowspan="2">Uang Kecil</th>
                    <th rowspan="2">Total</th>
                    <th rowspan="2">Saldo</th>
                    <th rowspan="2">Keterangan</th>
                </tr>
                <tr class="headings">
                    <th class="child">100.000</th>
                    <th class="child">75.000</th>
                    <th class="child">50.000</th>
                    <th class="child">20.000</th>
                    <th class="child">10.000</th>
                    <th class="child">5.000</th>
                </tr>
        </thead>
        <tbody>
            <span th:if="${listData.size() > 0}">
               <tr class="even pointer" id="tablerow" th:each="dataModel: ${listData}" th:classappend="${(dataModel.condition) == 'FORCED' ? 'isf' : ((dataModel.Condition) == 'CONTINGENCY' ? 'con' : '')}">
                <td class="left" th:text="${dataModel.refid}"></td>
                <td class="center" th:text="${dataModel.date}+' '+${dataModel.time}"></td>
                <td class="center" th:text="${dataModel.requestDate}+' '+${dataModel.requestTime}"></td>
                <td class="left" th:text="${dataModel.explanation}"></td>
                <td th:text="${dataModel.hundreds}"></td>
                   <td th:text="${dataModel.seventyFives}"></td>
                <td th:text="${dataModel.fiftys}"></td>
                <td th:text="${dataModel.twentys}"></td>
                <td th:text="${dataModel.tens}"></td>
                <td th:text="${dataModel.fives}"></td>
                <td th:text="${dataModel.smallMoney}"></td>
                <td th:text="${dataModel.total}"></td>
                <td th:text="${dataModel.balance}"></td>
                <td class="left" th:text="${dataModel.otherNote}"></td>
                </tr>
            </span>
            <span th:unless="${listData.size() > 0}">
                 <tr>
                    <td style="text-align: center" colspan="12">Belum ada data</td>
                </tr>
            </span>
            <span th:if="${isUseDenom} and ${listData.size() > 0}">
                <tr>
                    <td class="title" colspan="4">Total Denominasi</td>
                    <td th:text="${totHundreds}"></td>
                    <td th:text="${totSeventyFives}"></td>
                    <td th:text="${totFiftys}"></td>
                    <td th:text="${totTwentys}"></td>
                    <td th:text="${totTens}"></td>
                    <td th:text="${totFives}"></td>
                    <td th:text="${totSmallMoney}"></td>
                    <td class="title" colspan="2"/>
                </tr>
            </span>
        </tbody>
    </table>
</body>
</html>