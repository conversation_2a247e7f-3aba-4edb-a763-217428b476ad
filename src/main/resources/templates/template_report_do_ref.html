<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Log Book Biaya Rutin</title>
    <style>

        h1{
            font-size: 21px;
            font-weight: bold;
            color: #000000;
        }
        table.redTable tbody td.left{
            text-align: left;
        }
        table.redTable tbody td.center{
            text-align: center;
        }
        table.redTable {
            border: 2px solid black;
            background-color: #EEE7DB;
            width: 100%;
            border-collapse: collapse;
        }
        table.redTable tbody td {
            border: 1px solid black;
            padding: 3px;
            text-align: right;
        }
        table.redTable tbody tr:nth-child(even) {
            background: #F3C8BF;
        }
        table.redTable thead {
            background: #F37022;
        }
        table.redTable thead th {
            font-size: 19px;
            font-weight: bold;
            color: #FFFFFF;
            text-align: center;
            border: 2px solid rgb(0, 0, 0);
        }
        table.redTable thead th.child {
            font-size: 15px;
            font-weight: normal;
            color: #FFFFFF;
            text-align: center;
            border: 2px solid rgb(0, 0, 0);
        }
        table.redTable thead th.thchild{
            font-size : 12px;
        }
        table.redTable tbody tr.isf{
            background: #baebb5;
        }
    </style>
</head>
<body>
<h1>Referensi DANA OPERATIONAL</h1>
<h2></h2>
<h2></h2>

<table>
    <tr>
        <td>BM AREA</td>
        <td>: </td>
        <td th:text="${bmArea}"></td>
    </tr>
    <tr>
        <td>NAMA MMS / WISMA</td>
        <td>: </td>
        <td th:text="${mmsName}"></td>
    </tr>
    <tr>
        <td>KODE MMS / WISMA</td>
        <td>: </td>
        <td th:text="${mmsCode}"></td>
    </tr>
</table>

<h2></h2>

<table class="redTable">
    <thead>
    <tr>
        <th rowspan="2">No</th>
        <th rowspan="2">Referensi</th>
        <th rowspan="2">Tanggal</th>
        <th rowspan="2">Jenis Biaya Rutin</th>
        <th rowspan="2">Masuk</th>
        <th colspan="2">Keluar</th>
        <th rowspan="2">Sisa Saldo</th>
        <th rowspan="2">Pelaksana</th>
        <th rowspan="2">Pengelola</th>
        <th rowspan="2">Keterangan</th>
    </tr>
    <tr>
        <th class="thchild">Uang Diambil</th>
        <th class="thchild">Uang Terpakai</th>
    </tr>
    </thead>
    <tbody>
    <span th:if="${listData.size() > 0}">
                <tr class="even pointer" id="tablerow" th:each="dataModel: ${listData}" th:classappend="${(dataModel.isForced) == true ? 'isf' : ''}">
                    <td class="center" th:text="${dataModelStat.index+1}"></td>
                    <td class="left" th:text="${dataModel.reffCode}"></td>
                    <td class="center" th:text="${dataModel.transactionDate}"></td>
                    <td class="left" th:text="${dataModel.glName}"></td>
                    <span th:if="${dataModel.reportTransactionType == 'IN'}">
                    <td th:text="${dataModel.amount}"></td>
                    <td></td>
                    <td></td>
                    </span>
                    <span th:if="${dataModel.requestTypeDo == 'UTILIZATION'}">
                    <td></td>
                    <td th:text="${dataModel.amount}"></td>
                    <td></td>
                    </span>
                    <span th:if="${dataModel.requestTypeDo == 'ACTUALIZATION'}">
                    <td></td>
                    <td></td>
                    <td th:text="${dataModel.amount}"></td>
                    </span>
                    <span th:if="${dataModel.requestTypeDo == 'CANCELLED'}">
                    <td></td>
                    <td></td>
                    <td th:text="${dataModel.amount}"></td>
                    </span>
                    <td th:text="${dataModel.balanceAmount}"></td>
                    <td class="left" th:text="${dataModel.executedBy}"></td>
                    <td class="left" th:text="${dataModel.managedBy}"></td>
                    <td class="left" th:text="${dataModel.note}"></td>
                </tr>
            </span>
    <span th:unless="${listData.size() > 0}">
                 <tr>
                    <td style="text-align: center" colspan="15">Belum ada data</td>
                </tr>
            </span>
    </tbody>
</table>
</body>
</html>