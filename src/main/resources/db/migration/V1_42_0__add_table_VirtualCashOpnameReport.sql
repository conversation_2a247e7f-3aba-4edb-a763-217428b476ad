IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Virtual<PERSON>ashOpnameReport' AND xtype='U')
BEGIN
    CREATE TABLE VirtualCashOpnameReport(
        Id BIGINT PRIMARY KEY NOT NULL IDENTITY,
        MMSCode VARCHAR(20),
        MMSName VARCHAR(200),
        Area VARCHAR(500),
        CashOpnameDate DATETIME,
        CashOpnameLBOfficer VARCHAR(200),
        CashOpnameKWOfficer VARCHAR(200),
        CashOpnameKFOOfficer VARCHAR(200),
        CashOpnameKFOSupervisor VARCHAR(200),
        HundredBanknotes BIGINT,
        HundredBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        SeventyFiveBanknotes BIGINT,
        SeventyFiveBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        FiftyBanknotes BIGINT,
        FiftyBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        TwentyBank<PERSON> BIGINT,
        TwentyBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        TenBanknotes BIGINT,
        TenBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        FiveBanknotes BIGINT,
        FiveBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        TwoBanknotes BIGINT,
        TwoBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        OneBanknotes BIGINT,
        OneBanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        BanknotesAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        ThousandCoins BIGINT,
        ThousandCoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        FiveHundredCoins BIGINT,
        FiveHundredCoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        TwoHundredCoins BIGINT,
        TwoHundredCoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        OneHundredCoins BIGINT,
        OneHundredCoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        FiftyCoins BIGINT,
        FiftyCoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        CoinsAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        CashAmountTotal DECIMAL(22,4) DEFAULT 0.0000,
        AgendaBalance DECIMAL(22,4) DEFAULT 0.0000,
        T24Balance DECIMAL(22,4) DEFAULT 0.0000,
        CashAgendaDifference DECIMAL(22,4) DEFAULT 0.0000,
        CashSystemDifference DECIMAL(22,4) DEFAULT 0.0000,
        CashOpnameNotes VARCHAR(500),
        ManualDifferenceNotes VARCHAR(500),
        UnidentifiedDifferenceCondition BIT,
        VaultCondition BIT,
        CashBoxCondition BIT,
        VaultCashBoxNotes VARCHAR(500),
        PrimaryVaultKey BIT,
        PrimaryVaultKeyNotes VARCHAR(500),
        SpareVaultKey BIT,
        SpareVaultKeyNotes VARCHAR(500),
        VaultCombination BIT,
        VaultCombinationNotes VARCHAR(500),
        CombinationLastUpdate DATETIME,
        CombinationLastUpdateNotes VARCHAR(500),
        LKey BIT,
        LKeyNotes VARCHAR(500),
        PrimaryCashBoxKey BIT,
        PrimaryCashBoxKeyNotes VARCHAR(500),
        SpareCashBoxKey BIT,
        SpareCashBoxKeyNotes VARCHAR(500),
        CashBoxCombination BIT,
        CashBoxCombinationNotes VARCHAR(500),
        CashCounter BIT,
        CashCounterNotes VARCHAR(500),
        CashCounterFunctional BIT,
        CashCounterFunctionalNotes VARCHAR(500),
        ChecklistNotes VARCHAR(500),
        Approved BIT
        )
END