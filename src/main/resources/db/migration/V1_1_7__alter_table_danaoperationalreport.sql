IF EXISTS (SELECT * FROM sysobjects WHERE name='<PERSON>OperationalReport' AND xtype='U')
BEGIN
	DECLARE @sql NVARCHAR(MAX) = N'';

	select @sql += N'
	ALTER TABLE ' + 'dbo.DanaOperationalReport' +
		' DROP CONSTRAINT ' + QUOTENAME(dc.name) + ';'
	from sys.default_constraints dc
	inner join sys.tables t on t.[name] = 'DanaOperationalReport' and t.object_id = dc.parent_object_id

	EXEC sys.sp_executesql @sql

    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN Explanation;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN ReceivedAmount;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN RequestedAmount;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN RequestedBy;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN RequestedDate;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN ActualizationAmount;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN ActualizedBy;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN ActualizedDate;
    ALTER TABLE dbo.DanaOperationalReport DROP COLUMN DifferentAmount;
	
	ALTER TABLE DanaOperationalReport ADD RequestTypeDo VARCHAR(40);
    ALTER TABLE DanaOperationalReport ADD Amount DECIMAL(22,4) DEFAULT 0.0000;
    ALTER TABLE DanaOperationalReport ADD ExecutedBy VARCHAR(100);
    ALTER TABLE DanaOperationalReport ADD ManagedBy VARCHAR(100);
    ALTER TABLE DanaOperationalReport ADD BalanceTransactionType VARCHAR(20);
    ALTER TABLE DanaOperationalReport ADD ReffCode VARCHAR(20);
END