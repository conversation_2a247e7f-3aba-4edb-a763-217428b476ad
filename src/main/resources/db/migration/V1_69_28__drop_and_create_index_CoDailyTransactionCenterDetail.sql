IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='co_daily_transaction_center_detail_IDX' AND object_id = OBJECT_ID('CoDailyTransactionCenterDetail'))
        CREATE INDEX co_daily_transaction_center_detail_IDX ON CoDailyTransactionCenterDetail(CorrelationId, UpdatedBy);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='co_daily_transaction_center_detail_co_code_and_center_code_IDX' AND object_id = OBJECT_ID('CoDailyTransactionCenterDetail'))
        CREATE INDEX co_daily_transaction_center_detail_co_code_and_center_code_IDX
        ON CoDailyTransactionCenterDetail(CoCode, CenterCode);
