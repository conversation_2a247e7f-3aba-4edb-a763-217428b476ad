IF EXISTS (SELECT * FROM sysobjects WHERE name='CoverDanaMapping' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.CoverDanaMapping','ChangeReason') IS NULL
    ALTER TABLE CoverDanaMapping ADD ChangeReason VARCHAR(200) NULL;
END

IF EXISTS (SELECT * FROM sysobjects WHERE name='NoKomChangesLog' AND xtype='U')
BEGIN
    IF COL_LENGTH('dbo.NoKomChangesLog','ChangeReason') IS NULL
    ALTER TABLE NoKomChangesLog ADD ChangeReason VARCHAR(200) NULL;
END