IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashCountTransactionDifference' AND xtype='U')
CREATE TABLE CashCountTransactionDifference(
 	id BIGINT PRIMARY KEY NOT NULL IDENTITY,
 	CashCountId BIGINT NOT NULL,
 	DifferenceStatus VARCHAR(20),
 	HundredThousands INT DEFAULT 0,
 	HundredThousandsBundle INT DEFAULT 0,
 	SeventyFiveThousands INT DEFAULT 0,
 	SeventyFiveThousandsBundle INT DEFAULT 0,
 	FiftyThousands INT DEFAULT 0,
 	FiftyThousandsBundle INT DEFAULT 0,
 	TwentyThousands INT DEFAULT 0,
    TwentyThousandsBundle INT DEFAULT 0,
    TenThousands INT DEFAULT 0,
    TenThousandsBundle INT DEFAULT 0,
    FiveThousands INT DEFAULT 0,
    FiveThousandsBundle INT DEFAULT 0,
    TwoThousands INT DEFAULT 0,
    TwoThousandsBundle INT DEFAULT 0,
    OneThousands INT DEFAULT 0,
    OneT<PERSON>sandsBundle INT DEFAULT 0,
    FiveHundreds INT DEFAULT 0,
    FiveHundredsBundle INT DEFAULT 0,
    TwoHundreds INT DEFAULT 0,
    TwoHundredsBundle INT DEFAULT 0,
    OneHundreds INT DEFAULT 0,
    OneHundredsBundle INT DEFAULT 0,
    SmallMoney INT DEFAULT 0,
    TotalAmount DECIMAL(22,4) DEFAULT 0.0000,
    Notes VARCHAR(500),
    CreatedDate DATETIME,
    CreatedBy VARCHAR(50),
    UpdatedDate DATETIME,
    UpdatedBy VARCHAR(50)
 )