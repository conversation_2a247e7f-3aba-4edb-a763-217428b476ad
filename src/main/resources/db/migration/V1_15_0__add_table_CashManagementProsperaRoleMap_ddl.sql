IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashManagementProsperaRoleMap' AND xtype='U')
    CREATE TABLE CashManagementProsperaRoleMap (
        ID bigint IDENTITY(1, 1) NOT NULL,
        ProsperaRoleId INTEGER NOT NULL,
        CashManagementRoleName VARCHAR(100) NOT NULL,
        CONSTRAINT cashManagementProsperaRoleMapPk PRIMARY KEY CLUSTERED (ID ASC)
    );
GO
IF NOT EXISTS(SELECT * FROM sys.indexes WHERE name='IX_CashManagementRoleName' AND object_id = OBJECT_ID('CashManagementProsperaRoleMap'))
    CREATE INDEX IX_CashManagementRoleName ON CashManagementProsperaRoleMap(CashManagementRoleName);
GO
IF NOT EXISTS(SELECT * FROM sys.indexes WHERE name='IX_ProsperaRoleId' AND object_id = OBJECT_ID('CashManagementProsperaRoleMap'))
    CREATE INDEX IX_ProsperaRoleId ON CashManagementProsperaRoleMap(ProsperaRoleId);
GO