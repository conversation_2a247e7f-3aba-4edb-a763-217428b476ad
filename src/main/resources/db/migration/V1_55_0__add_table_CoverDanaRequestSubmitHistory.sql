IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CoverDanaRequestSubmitHistory' AND xtype='U')
  CREATE TABLE CoverDanaRequestSubmitHistory (
    Id BIGINT primary key not null identity,
    CoverDanaId BIGINT,
    ManualInput BIT,
    MMSCode NVARCHAR(20),
    MMSName NVARCHAR(200),
    KFOCode NVARCHAR(20),
    KFOName NVARCHAR(200),
    BranchCode NVARCHAR(20),
    RequestAmount DECIMAL(22,4),
    Type NVARCHAR(50),
    Vendor NVARCHAR(50),
    SumberDanaGroup NVARCHAR(50),
    SumberDanaName NVARCHAR(100),
    RequestedFor DATETIME,
    SubmittedBy NVARCHAR(100),
    SubmittedOn DATETIME
  )

