<configuration debug="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} %X{X-B3-TraceId:-} %X{X-B3-SpanId:-} - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <logger name="com" level="INFO">
    </logger>

    <logger name="org" level="INFO">
    </logger>

    <logger name="io" level="INFO">
    </logger>

    <logger name="net" level="INFO">
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

    <root level="debug">
        <appender-ref ref="STDOUT"/>
    </root>



</configuration>