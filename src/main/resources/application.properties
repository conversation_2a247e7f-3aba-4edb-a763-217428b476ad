## PLACE ALL THE COMMONLY USED PROPERTIES IN THIS FILE

# to avoid using underscores for camelCasing of names
spring.jpa.hibernate.naming-strategy=org.hibernate.cfg.EJB3NamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

# datasource
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.jpa.show-sql=false
spring.jpa.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.hibernate.ddl-auto=none

#daily transaction
co.daily.transaction.date.range=7
daily.transaction.history.month.range=1

#lemari besi
lemari.besi.history.date.range=1

#dana operational
dana.operational.history.date.range=6

#documenet report
document.report.list.mms.date.range.day=14
document.report.list.kfo.date.range.day=3
logging.level.org.docx4j=OFF

#general ledger
general.ledger.dana.operational.month.range=3
general.ledger.dana.operational.biaya.rutin.glcode=962752
general.ledger.dana.operational.fasilitas.mms.glcode=142509

#general ledger PRS related
general.ledger.lemari.besi.glcode=800910
general.ledger.kas.wisma.glcode=800400

#prospera
prospera.communication.switch.on=yes
prospera.communication.type=email
prospera.communication.approved.by=agendaku

#mprospera
mprospera.communication.switch.on=yes
mprospera.alter.system.date=no
mprospera.communication.altered.system.date=20210517

#T24
t24.communication.switch.on=yes
t24.communication.user.id=AGENDAKU

#Xlink
xlink.communication.switch.on=yes


# place all the local specific configurations in this file. NOTE: The properties shall override those present in application.properties
# Server Properties
server.port=${SERVER_PORT:8080}
server.servlet.context-path=${CONTEXT_PATH:/beta-cash-management}

# Primary Datasource
spring.datasource.driverclassname=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.jdbc-url=${SPRING_DATASOURCE_URL:*************************************************************************}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME:Selayar.dbo}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD:P@ssw0rd}

# Secondary Datasource
spring.datasource2.driverclassname=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource2.jdbc-url=${SPRING_DATASOURCE_URL_2:*************************************************************************}
spring.datasource2.username=${SPRING_DATASOURCE_USERNAME_2:Selayar.dbo}
spring.datasource2.password=${SPRING_DATASOURCE_PASSWORD_2:P@ssw0rd}

# Prospera Datasource
prospera.datasource.driverclassname=com.mysql.cj.jdbc.Driver
prospera.datasource.jdbc-url=${PROSPERA_DATASOURCE_URL:****************************************}
prospera.datasource.username=${PROSPERA_DATASOURCE_USERNAME:tepatuatrw}
prospera.datasource.password=${PROSPERA_DATASOURCE_PASSWORD:Pr05p3r!+y}

spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=100

# Firebase
firebase.server.key=key=AAAANUt2Lqc:APA91bGiiWx9M4ZV5Fd5Oqyf_Kkuo7prdhn3rrAZgaGIeNVmo2k0wGaN7_IRCDtoyDBst1FaFvB6bWL7FKA5uZ76fXvxU8Gvkzt_O06GCNbh_aBtfShtClizFGuE1Q5hxX-IhgK-maEo
firebase.fcm.url=https://fcm.googleapis.com/fcm/send

# MinIO Storage
storage.path=${IMAGE_STORAGE_PATH:https://storage-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/bucket01/agendaku/helpCenter/}

spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:P@ssw0rd12345}
spring.mail.properties.mail.smtp.auth=true
spring.mail.host=${MAIL_HOST:smtp.office365.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.properties.mail.smtp.starttls.enable=true

spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000

# Flyway properties
spring.flyway.enabled=true
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.url=${spring.datasource.jdbc-url}
spring.flyway.table=cash_management_schema_history
spring.flyway.locations=classpath:db/migration

# cms application service URI via service discovery
services.cms.url=${CMS_URL:http://cms.agendaku-dirty.svc:9001/dev-cms}

# companion application service URI via service discovery
services.companion.url=${COMPANION_URL:https://companion-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/dev-companion}

cm.bypass.login = true

cm.validator.bypass=${CM_VALIDATOR_BYPASS:false}

#cover dana
cover.dana.base.folder=template/
cover.dana.write.folder=resources/print/
cover.dana.uploader.folder=resources/uploader/
cover.dana.mapping.file=DataMapping_20191009.xlsx
cover.dana.user.persona.file=DataCO.xlsx
cover.dana.insurance.file=Deklarasi_Asuransi.docx
kw.daily.insurance.file=template/Deklarasi_Asuransi_Permintaan.docx
cover.dana.overlimit.file=Deklarasi_Asuransi_Overlimit_LB.docx
cover.dana.overlimit.gabungan.file=Deklarasi_Asuransi_Overlimit_LB_Gabungan.docx
cover.dana.fpcd.vendor.file=FPCD_Vendor.docx
cover.dana.fpcd.mms.file=FPCD_MMS.DOCX
cover.dana.json.email.file=mail.json
cover.dana.logbook.file=template/Logbook_CoverDana.docx
cover.dana.request.currency=Rp
cover.dana.range.date.estimation = 6
cover.dana.amount.rounded = false

#LB limit
lemaribesi.limit.default=250000000

#Cash Opname
cash.opname.file=BERITA_ACARA_CASH_OPNAME.docx
virtual.cash.opname.file=Laporan_BACO_Checklist.docx
virtual.cash.opname.loa.file=SURAT_TUGAS_VCO.docx

#Stock Opname
stock.opname.file=template/BA_Stock_Opname.docx

#FPB
fpb.file=template/FPB.docx

#PRS
rekap.prs.file=template/Rekap_Persiapan_Pulang_PRS.docx

#RPCD
rpcd.kfo.file=template/RPCD_kfo.docx
rpcd.branch.file=template/RPCD_branch.docx

#cover dana email
cover.dana.sender.mail=${COVER_DANA_SENDER_MAIL:<EMAIL>}
cover.dana.sender.pass=${COVER_DANA_SENDER_PASS:P@ssw0rd12345}
cover.dana.mail.host=${COVER_DANA_MAIL_HOST:**********}
cover.dana.mail.port=${COVER_DANA_MAIL_PORT:25}
cover.dana.sharia.mail=${COVER_DANA_SHARIA_MAIL:<EMAIL>}

#Email Agendaku (For Loopback Email)
agendaku.mail.address=${cover.dana.sender.mail}

#user authentication URL location
user.auth.url.location=${services.companion.url}${COMPANION_APPROVE:/user/approve}
user.upmAuth.url.location=${services.companion.url}/userUpm/approve
user.cisAuth.url.location=${services.companion.url}/userCis/approve
user.otor.url.location=${services.companion.url}/user/otor

#general ledger
general.ledger.mapping.file=GL_Code.xlsx

#Automatic Journal
general.ledger.prospera.location=${PROSPERA_LOCATION:http://***********:8080/prosperarest/services/journal/create/}
general.ledger.prospera.location.cover.in=${PROSPERA_LOCATION_COVER_IN:http://***********:8080/prosperarest/services/accounting/coverIn}
general.ledger.prospera.location.cover.out=${PROSPERA_LOCATION_COVER_OUT:http://***********:8080/prosperarest/services/accounting/coverOut}

general.ledger.prospera.manual.journal.location=${isb.lombok.location}/account/cover-in-out/3.0.0/manual

#isb Lombok
isb.lombok.api.key=${ISB_LOMBOK_API_KEY:d2745ad134cf09a844d2d8413aa1ab2b}
isb.lombok.location=${PROSPERA_LOMBOK_LOCATION:https://api-beta-v2.apps.btpnsdev1.c3vu.p1.openshiftapps.com}

#xlink
journal.xlink.cover.dana.post=${PROSPERA_LOMBOK_LOCATION_XLINK:https://api-switching-beta-v2.apps.btpnsyariah.com}/transaction/2.0.0/transfer-online
xlink.lombok.api.key=${LOMBOK_XLINK_API_KEY:1322b495a4cba509ddd3cd586eb40ef5}

#wow Ib Isb
isb.wow.ib.account=${isb.lombok.location}/fund/transaction/2.0.0/account/
isb.wow.ib.account.history=${isb.lombok.location}/fund/transaction/2.0.0/account/history
isb.wow.ib.account.inquiry=${isb.lombok.location}/fund/transaction/2.0.0/account/inquiry

#Journal Prospera Lombok
journal.prospera.lombok.posting=${isb.lombok.location}/account/cover-in-out/3.0.0/posting
journal.prospera.lombok.reversal=${isb.lombok.location}/account/cover-in-out/3.0.0/reversal

#Journal T24 Lombok
journal.T24.lombok.posting=${isb.lombok.location}/account/cover-in-out/3.0.0/posting
journal.T24.lombok.reversal=${isb.lombok.location}/account/cover-in-out/3.0.0/reversal

general.ledger.mprospera.location.token=${MPROSPERA_LOCATION_TOKEN:http://10.7.17.127:9380/agendaku/webservice/getToken}
general.ledger.mprospera.location.cover.in=${MPROSPERA_LOCATION_COVER_IN:http://10.7.17.127:9380/agendaku/webservice/getRequestDana}
general.ledger.mprospera.location.cover.out=${MPROSPERA_LOCATION_COVER_OUT:http://10.7.17.127:9380/agendaku/webservice/getPRSMoney}
general.ledger.mprospera.location.prs=${MPROSPERA_LOCATION_PRS:http://10.7.17.127:9380/agendaku/webservice/prs/}
general.ledger.mprospera.location.penagihan=${MPROSPERA_LOCATION_PENAGIHAN:http://10.7.17.127:9380/agendaku/webservice/penagihan/}

#T24 API
general.ledger.t24.location.echo=${T24_LOCATION_ECHO:https://10.7.17.167:443/btpns/agendaku/echo}
general.ledger.t24.location.ci.co=${T24_LOCATION_CI_CO:https://10.7.17.167:443/btpns/agendaku/cashinout/t24}
general.ledger.t24.trx.type.ci=${T24_TRX_TYPE_CI:105501}
general.ledger.t24.trx.type.co=${T24_TRX_TYPE_CO:105401}

#DAM API
dam.location.cover.dana=${DAM_LOCATION_COVER_DANA:https://dam-perf-dirty.apps.nww.syariahbtpn.com/activity-drafts/create/by-co-code}

#WOW API
wow.base.url=${WOW_BASE_URL:http://10.7.77.64:8181}
wow.account.inquiry=/btpns/wowib/account/inquiry

#user dummy kfo
user.dummy.kfo.kfoCode = 5038
user.dummy.kfo.kfoName = KFO DRAMAGA

#kafka configuration
spring.kafka.bootstrap-servers=${KAFKA_URL:dirty-kafka-bootstrap.amq-streams-dev.svc.cluster.local:9093}
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.heartbeat-interval=20000
spring.kafka.consumer.properties.session.timeout.ms=60000
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.client-id=demo-consumer-client-id
spring.kafka.consumer.auto-startup=${KAFKA_STATUP:false}
spring.kafka.key.truststore.location=/usr/app/key/kafka-truststore.jks
spring.kafka.key.truststore.password=${KEYSTORE_PASSWORD:password}
spring.kafka.ssl.enabled=${KAFKA_SSL_ENABLED:true}
kafka.jaas.config=${JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="agendaku-beta" password="dkPZvhGGF12l";}


agendaku.kafka.consumer.auto-startup=${KAFKA_STATUP:false}

#Kafka Topic
agendaku.kafka.controller.enabled=${KAFKA_CONTROLLER:false}

agendaku.kafka.topic.cdt.detail=agendaku.cdt.detail

agendaku.kafka.topic.collection.result=fin.collection.resultv2
agendaku.kafka.topic.collection.result.group=${agendaku.kafka.topic.collection.result}-001

agendaku.kafka.topic.prs.rctt=fin.prs.rctt
agendaku.kafka.topic.prs.rctt.group=${agendaku.kafka.topic.prs.rctt}-001

agendaku.kafka.topic.prs.schedule.v3=fin.prs.schedule.v3
agendaku.kafka.topic.prs.schedule.v3.group={agendaku.kafka.topic.prs.schedule.v3}-001

agendaku.kafka.topic.prs.result=fin.prs.result
agendaku.kafka.topic.prs.result.group=${agendaku.kafka.topic.prs.result}-001

agendaku.kafka.topic.mitra.activity=agendaku.mitra.activity
agendaku.kafka.topic.mitra.activit.group=${agendaku.kafka.topic.mitra.activity}-001

agendaku.kafka.topic.likuiditas.deposit.now=agendaku.likuiditas.integration.deposit.now
agendaku.kafka.topic.likuiditas.deposit.now.group=${agendaku.kafka.topic.likuiditas.deposit.now}-001
agendaku.kafka.topic.likuiditas.post=agendaku.likuiditas.cash.in.out.integration

agendaku.kafka.topic.mitra.activity.cancel=agendaku.mitra.cancel
agendaku.kafka.topic.mitra.activity.cancel.group=${agendaku.kafka.topic.mitra.activity.cancel}-001

agendaku.kafka.topic.misool.MsMMs=misool.MsMMs
agendaku.kafka.topic.misool.MsMMs.group=${agendaku.kafka.topic.mitra.activity}-001

agendaku.kafka.topic.download.request=agendaku.download.request
agendaku.kafka.topic.download.request.group=${agendaku.kafka.topic.download.request}-001

agendaku.kafka.topic.cdc.TrxFinancingPartialPending=${TrxFinancingPartialPending:sl.dbo.TrxFinancingPartialPending}
agendaku.kafka.topic.cdc.TrxFinancingPartialPending.group=${agendaku.kafka.topic.cdc.TrxFinancingPartialPending}-001
#agendaku.kafka.topic.cdc.TrxSavingsWithdrawalPlan=${TrxSavingsWithdrawalPlan}
#agendaku.kafka.topic.cdc.TrxSavingsWithdrawalPlan.group=${agendaku.kafka.topic.lombok.TrxSavingsWithdrawalPlan}-001

agendaku.kafka.topic.notification=agendaku.notification
agendaku.kafka.topic.notification.group=${agendaku.kafka.topic.notification}-001

by.pass.email=true

server.max-http-header-size=${MAX_HEADER_SIZE:16384}

#DEBUG
debug.system.sleep.second=0

agendaku.kafka.topic.core.banking.integration=agendaku.cashmanagement.integration.corebank

agendaku.likuiditas.autoposting.toggle=${LikuiditasAutopostingToggle:}
agendaku.likuiditas.whitelist.all.toggle=${LikuiditasWhitelistAllToggle:false}

# Swagger toggle
springfox.documentation.auto-startup=${SPRINGDOC_UI_TOGGLE:false}
springdoc.swagger-ui.doc-expansion=none

#time processing
time.processing.active=false

#crossorigin url
cross.origin.url=https://core-agendaku-south.apps.south.syariahbtpn.com,https://core-agendaku-north.apps.north.syariahbtpn.com,http://localhost:3000,http://localhost:3001,https://agendaku.apps.btpnsyariah.com,https://core-agendaku-dirty.apps.nww.syariahbtpn.com,https://core-agendaku-alpha.apps.nww.syariahbtpn.com,https://core-agendaku-beta.apps.nww.syariahbtpn.com,https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com


mail.api.version=${MAIL_API_VERSION:2.1.0}
mail.sender=${MAIL_SENDER:<EMAIL>}
mail.to.testing=${MAIL_TO_TESTING:<EMAIL>}
mail.cc.testing=${MAIL_CC_TESTING:<EMAIL>}
mail.bcc.testing=${MAIL_BCC: }
mail.enable=${MAIL_ENABLE:true}
mail.testing=${MAIL_Testing:true}

channel.id=6021
node=BTPNS
terminal.id=agendaku
terminal.name=agendaku
acq.id=547
orgunit.id=547

# MinIO Storage
minio.url=${STORAGE_PATH:https://storage-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com}
minio.bucket=${BUCKET_NAME:bucket01}
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true